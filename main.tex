%-------------------------
% Resume in Latex
% Author : <PERSON>
% Based off of: https://github.com/sb2nov/resume
% License : MIT
%------------------------

\documentclass[letterpaper,11pt]{article}

\usepackage{fontspec}
\usepackage{xeCJK}
\setCJKmainfont{STHeiti} 
\usepackage{latexsym}
\usepackage[empty]{fullpage}
\usepackage{titlesec}
\usepackage{marvosym}
\usepackage[usenames,dvipsnames]{color}
\usepackage{verbatim}
\usepackage{enumitem}
\usepackage[hidelinks]{hyperref}
\usepackage{fancyhdr}
\usepackage[english]{babel}
\usepackage{tabularx}
\usepackage{xstring}
\usepackage{ifthen}
\usepackage{fontawesome5}
% \input{glyphtounicode}


%----------FONT OPTIONS----------
% sans-serif
% \usepackage[sfdefault]{FiraSans}
% \usepackage[sfdefault]{roboto}
% \usepackage[sfdefault]{noto-sans}
% \usepackage[default]{sourcesanspro}
\usepackage[sfdefault]{carlito}

% serif
% \usepackage{CormorantGaramond}
% \usepackage{charter}


\pagestyle{fancy}
\fancyhf{} % clear all header and footer fields
\fancyfoot{}
\renewcommand{\headrulewidth}{0pt}
\renewcommand{\footrulewidth}{0pt}

% Adjust margins
\addtolength{\oddsidemargin}{-0.5in}
\addtolength{\evensidemargin}{-0.5in}
\addtolength{\textwidth}{1in}
\addtolength{\topmargin}{-.5in}
\addtolength{\textheight}{1.0in}

\urlstyle{same}

\raggedbottom
\raggedright
\setlength{\tabcolsep}{0in}

% Sections formatting
\titleformat{\section}{
  \vspace{-4pt}\scshape\raggedright\large
}{}{0em}{}[\color{black}\titlerule \vspace{-5pt}]

% Ensure that generate pdf is machine readable/ATS parsable
% \pdfgentounicode=1

% Phone number display control (default: true)
\providecommand{\showphone}{true}
\providecommand{\phonenumber}{0000-0000}

%-------------------------
% Custom commands
\newcommand{\resumeItem}[1]{
  \item\small{
    {#1 \vspace{-2pt}}
  }
}

\newcommand{\resumeSubheading}[4]{
  \vspace{-2pt}\item
    \begin{tabular*}{0.97\textwidth}[t]{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\
      \textit{\small#3} & \textit{\small #4} \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeEducation}[6]{
  \vspace{-2pt}\item
    \begin{tabular*}{0.97\textwidth}[t]{l@{\extracolsep{\fill}}r}
      \textbf{#1} & #2 \\
      \textit{\small#3} & \textit{\small #4} \\
      \textit{\small#5} & \textit{\small #6} \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeSubSubheading}[2]{
    \item
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \textit{\small#1} & \textit{\small #2} \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeProjectHeading}[2]{
    \item
    \begin{tabular*}{0.97\textwidth}{l@{\extracolsep{\fill}}r}
      \small#1 & \textit{\small #2} \\
    \end{tabular*}\vspace{-7pt}
}

\newcommand{\resumeSubItem}[1]{\resumeItem{#1}\vspace{-4pt}}

\renewcommand\labelitemii{$\vcenter{\hbox{\tiny$\bullet$}}$}

\newcommand{\resumeSubHeadingListStart}{\begin{itemize}[leftmargin=0.15in, label={}]}
\newcommand{\resumeSubHeadingListEnd}{\end{itemize}}
\newcommand{\resumeItemListStart}{\begin{itemize}}
\newcommand{\resumeItemListEnd}{\end{itemize}\vspace{-5pt}}

%-------------------------------------------
%%%%%%  RESUME STARTS HERE  %%%%%%%%%%%%%%%%%%%%%%%%%%%%


\begin{document}

%----------HEADING----------
% \begin{tabular*}{\textwidth}{l@{\extracolsep{\fill}}r}
%   \textbf{\href{http://sourabhbajaj.com/}{\Large Sourabh Bajaj}} & Email : \href{mailto:<EMAIL>}{<EMAIL>}\\
%   \href{http://sourabhbajaj.com/}{http://www.sourabhbajaj.com} & Mobile : ******-456-7890 \\
% \end{tabular*}

\begin{center}
    \textbf{\Huge \scshape Chen Xiang } \textbf{\huge \scshape 陈想}\\ \vspace{1pt}
    \small
    \ifthenelse{\equal{\showphone}{true}}{\raisebox{-1pt}{\faPhone} \space \phonenumber \space $|$}{}
    \raisebox{-1pt}{\faEnvelope} \space \href{mailto:<EMAIL>}{\underline{<EMAIL>}} $|$
    \raisebox{-1pt}{\faGlobe} \space \href{https://cxiang.site}{\underline{cxiang.site}} $|$
    \raisebox{-1pt}{\faGithub} \space \href{https://github.com/Xiang-CH}{\underline{github.com/Xiang-CH}}
\end{center}


%-----------EDUCATION-----------
\section{Education}
  \resumeSubHeadingListStart
    \resumeEducation
      {\href{https://hku.hk/}{The University of Hong Kong}}{Hong Kong}
      {Master of Science in Computer Science}{Sep. 2025 -- Present}
      {Bachelor of Engineering in Computer Science}{Sep. 2021 -- June 2025}
      \resumeItemListStart
        \resumeItem{First Class Honours, Dean's Honours List (2021-2022)}
        \resumeItem{Relevant Coursework: Machine Learning, Computer Vision, Operating Systems, Object-Oriented Programming, Data Science, Software Engineering, Algorithm Design, Databases, Network Communications, Natural Language Processing, Deep Learning}
      \resumeItemListEnd
  \resumeSubHeadingListEnd


%-----------EXPERIENCE-----------
\section{Experience}
  \resumeSubHeadingListStart

    \resumeSubheading
      {\href{https://www.linkedin.com/school/universityofhongkong}{The University of Hong Kong}}{Hong Kong}
      {Research Assistant II}{Jun 2025 -- Present}
      \resumeItemListStart
        \resumeItem{Development of Legal AI tools for teaching and learning purposes with the Law and Technology Centre at HKU}
        \resumeItem{Processed raw documents and built a relational semantic database for Hong Kong legal documents including articles, legislations, and judgments to facilitate efficient information retrieval for Retrieval Augmented Large Language Models in legal applications.}
      \resumeItemListEnd
      
   \resumeSubSubheading
    {Student Research Assistant}{Jun 2023 -- May 2024, Sep 2024 -- May 2025}
    \resumeItemListStart
       \resumeItem{Designed and tought multiple hands-on workshops on AI projects at the Innovation Wing at HKU. Including creating \href{https://github.com/Xiang-CH/Innochat-Template}{\underline{custom chatbots}} with Azure services, and building multi-modal Retrieval Augmented Generation (RAG) applications, etc.}
        \resumeItem{Worked on multiple student lead AI projects, including \href{https://clic-search.vercel.app/}{\underline{CLIC-Search}}: a semantic search engine for the Community Legal Information Center; \href{https://aiha.vercel.app/}{{\underline{AI Historian Assistant}}: a multi-modal history research assistant; etc.}}
    \resumeItemListEnd


    \resumeSubheading
      {\href{https://www.linkedin.com/company/iflytekcoltd.}{iFLYTEK Co., Ltd.}}{Shenzhen}
      {Assistant Software Development Engineer}{June 2024 -- Aug 2024}
      \resumeItemListStart
        \resumeItem{Involved in the development of AI English speaking tutor and AI picture book companion Large Language Model (LLM) solution in the education product line.}
        \resumeItem{Developed and maintained the application pipelines using \emph{Node-RED} for custom needs based on requirements.}
        \resumeItem{Automated data engineering procedures for model fine-tuning using \emph{Python} scripts and optimized the prompt tuning process using tools such as \emph{promptfoo}.}
      \resumeItemListEnd

  \resumeSubHeadingListEnd


%-----------PROJECTS-----------
\section{Projects}
    \resumeSubHeadingListStart
      \resumeProjectHeading
          {\textbf{\href{https://hk-legal-ai.vercel.app/search}{\underline{CLIC-Chats}}} $|$ \emph{Next.js, Prisma, MS-SQL, Azure, LLM, RAG}}{Jul 2025 -- Present}
          \resumeItemListStart
            \resumeItem{Developed a full-stack web application using with Flask serving a REST API with React as the frontend}
            \resumeItem{Implemented GitHub OAuth to get data from user’s repositories}
            \resumeItem{Visualized GitHub data to show collaboration}
            \resumeItem{Used Celery and Redis for asynchronous tasks}
          \resumeItemListEnd

      \resumeProjectHeading
          {\textbf{Simple Paintball} $|$ \emph{Spigot API, Java, Maven, TravisCI, Git}}{May 2018 -- May 2020}
          \resumeItemListStart
            \resumeItem{Developed a Minecraft server plugin to entertain kids during free time for a previous job}
            \resumeItem{Published plugin to websites gaining 2K+ downloads and an average 4.5/5-star review}
            \resumeItem{Implemented continuous delivery using TravisCI to build the plugin upon new a release}
            \resumeItem{Collaborated with Minecraft server administrators to suggest features and get feedback about the plugin}
          \resumeItemListEnd
    \resumeSubHeadingListEnd



%
%-----------PROGRAMMING SKILLS-----------
\section{Technical Skills}
 \begin{itemize}[leftmargin=0.15in, label={}]
    \small{\item{
     \textbf{Languages}{: Java, Python, C/C++, SQL (Postgres), JavaScript, HTML/CSS, R} \\
     \textbf{Frameworks}{: React, Node.js, Flask, JUnit, WordPress, Material-UI, FastAPI} \\
     \textbf{Developer Tools}{: Git, Docker, TravisCI, Google Cloud Platform, VS Code, Visual Studio, PyCharm, IntelliJ, Eclipse} \\
     \textbf{Libraries}{: pandas, NumPy, Matplotlib}
    }}
 \end{itemize}


%-------------------------------------------
\end{document}
